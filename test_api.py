#!/usr/bin/env python3
"""
API测试脚本 - 测试新实现的用户和角色管理API
"""

import requests
import json
import jwt
import uuid
from datetime import datetime, timedelta

# 配置
BASE_URL = "http://127.0.0.1:8080"
JWT_SECRET = "dev-jwt-secret-key-for-testing-only"

def create_test_jwt(user_id, email, provider="admin"):
    """创建测试用的JWT token"""
    payload = {
        'user_id': str(user_id),
        'email': email,
        'provider': provider,
        'exp': datetime.utcnow() + timedelta(days=1),
        'iat': datetime.utcnow()
    }
    return jwt.encode(payload, JWT_SECRET, algorithm='HS256')

def test_user_profile_api():
    """测试用户信息API"""
    print("🧪 测试用户信息API...")
    
    # 创建测试JWT token
    test_user_id = str(uuid.uuid4())
    token = create_test_jwt(test_user_id, "<EMAIL>")
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # 测试获取用户信息
    print("📋 测试 GET /api/v1/users/me")
    response = requests.get(f"{BASE_URL}/api/v1/users/me", headers=headers)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        print("✅ 获取用户信息成功")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    else:
        print(f"❌ 获取用户信息失败: {response.text}")
    
    # 测试更新用户信息
    print("\n📝 测试 PUT /api/v1/users/me")
    update_data = {
        "nickname": "测试管理员",
        "avatar_url": "https://example.com/avatar.jpg"
    }
    response = requests.put(f"{BASE_URL}/api/v1/users/me", 
                           headers=headers, 
                           json=update_data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        print("✅ 更新用户信息成功")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
    else:
        print(f"❌ 更新用户信息失败: {response.text}")

def test_character_api():
    """测试角色管理API"""
    print("\n🎭 测试角色管理API...")
    
    # 创建测试JWT token
    test_user_id = str(uuid.uuid4())
    token = create_test_jwt(test_user_id, "<EMAIL>")
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # 首先获取角色列表
    print("📋 测试 GET /api/v1/characters/")
    response = requests.get(f"{BASE_URL}/api/v1/characters/", headers=headers)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        characters = response.json().get('characters', [])
        print(f"✅ 获取角色列表成功，共 {len(characters)} 个角色")
        
        if characters:
            # 测试获取第一个角色的详情
            character_id = characters[0]['id']
            print(f"\n📋 测试 GET /api/v1/characters/{character_id}")
            response = requests.get(f"{BASE_URL}/api/v1/characters/{character_id}", headers=headers)
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                print("✅ 获取角色详情成功")
                print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            else:
                print(f"❌ 获取角色详情失败: {response.text}")
        else:
            print("ℹ️ 暂无角色数据，跳过角色详情测试")
    else:
        print(f"❌ 获取角色列表失败: {response.text}")

def main():
    """主函数"""
    print("🚀 开始API测试...")
    print(f"📍 测试服务器: {BASE_URL}")
    
    try:
        # 测试服务器连接
        response = requests.get(f"{BASE_URL}/admin/", timeout=5)
        print("✅ 服务器连接正常")
    except requests.exceptions.RequestException as e:
        print(f"❌ 服务器连接失败: {e}")
        print("请确保Django开发服务器正在运行: python manage.py runserver 8080")
        return
    
    # 运行测试
    test_user_profile_api()
    test_character_api()
    
    print("\n🎉 API测试完成！")

if __name__ == "__main__":
    main()
