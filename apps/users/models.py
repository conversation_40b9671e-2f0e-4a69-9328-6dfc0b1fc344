import uuid
from django.contrib.auth.models import AbstractBaseUser, BaseUserManager, PermissionsMixin
from django.db import models
from django.utils import timezone


class UserManager(BaseUserManager):
    def create_user(self, email, provider, provider_user_id, **extra_fields):
        if not email:
            raise ValueError('The Email field must be set')
        email = self.normalize_email(email)
        user = self.model(
            email=email,
            provider=provider,
            provider_user_id=provider_user_id,
            **extra_fields
        )
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        """创建超级用户 - 自动设置为 admin provider"""
        # 确保provider为admin
        extra_fields['provider'] = 'admin'

        user = self.model(
            email=self.normalize_email(email),
            provider_user_id=str(uuid.uuid4()),
            **extra_fields
        )
        user.set_password(password)
        user.save(using=self._db)
        return user


class User(AbstractBaseUser, PermissionsMixin):
    PROVIDER_CHOICES = [
        ('google', 'Google'),
        ('apple', 'Apple'),
        ('admin', 'Admin'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    provider = models.CharField(max_length=20, choices=PROVIDER_CHOICES)
    provider_user_id = models.CharField(max_length=255)
    email = models.EmailField(unique=True)
    nickname = models.CharField(max_length=100, blank=True)
    avatar_url = models.URLField(blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    is_active = models.BooleanField(default=True)
    
    # 每日限额配置
    daily_generation_limit = models.PositiveIntegerField(
        default=5,
        help_text="每日免费生成3D角色的次数限制"
    )
    
    objects = UserManager()
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []
    
    class Meta:
        unique_together = ['provider', 'provider_user_id']
        db_table = 'users'
    
    @property
    def is_staff(self):
        """管理员才能访问 Django admin"""
        return self.provider == 'admin'
    
    @property
    def is_superuser(self):
        """管理员拥有所有权限"""
        return self.provider == 'admin'
    
    def get_today_generation_count(self):
        """获取今日已生成的角色数量"""
        from django.utils import timezone
        today_start = timezone.now().replace(hour=0, minute=0, second=0, microsecond=0)
        return self.generation_tasks.filter(created_at__gte=today_start).count()
    
    def get_remaining_generations_today(self):
        """获取今日剩余生成次数"""
        used = self.get_today_generation_count()
        return max(0, self.daily_generation_limit - used)
    
    def can_generate_today(self):
        """检查今日是否还能生成角色"""
        # 管理员无限制
        if self.is_superuser:
            return True
        return self.get_remaining_generations_today() > 0
    
    def __str__(self):
        return f"{self.email} ({self.provider})"
